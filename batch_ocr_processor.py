import base64
import os
import json
import time
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from mistralai import Mistral

# Load environment variables
load_dotenv()

def encode_image(image_path):
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except FileNotFoundError:
        print(f"Error: The file {image_path} was not found.")
        return None
    except Exception as e:
        print(f"Error encoding {image_path}: {e}")
        return None

def get_image_files(folder_path):
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

    folder = Path(folder_path)
    if not folder.exists():
        print(f"Error: Folder {folder_path} does not exist.")
        return []

    image_files = []
    for file_path in folder.iterdir():
        if file_path.is_file():
            if file_path.suffix.lower() in image_extensions:
                image_files.append(str(file_path))

    return sorted(image_files)

def process_single_image(client, image_path):
    try:
        print(f"Processing: {image_path}")

        # Encode image to base64
        base64_image = encode_image(image_path)
        if base64_image is None:
            return {
                "error": "Failed to encode image",
                "status": "failed"
            }

        # Determine MIME type based on file extension
        file_ext = Path(image_path).suffix.lower()
        if file_ext in ['.jpg', '.jpeg']:
            mime_type = "image/jpeg"
        elif file_ext == '.png':
            mime_type = "image/png"
        else:
            mime_type = "image/jpeg"  # Default fallback

        # Call Mistral OCR API
        ocr_response = client.ocr.process(
            model="mistral-ocr-latest",
            document={
                "type": "image_url",
                "image_url": f"data:{mime_type};base64,{base64_image}"
            },
            include_image_base64=False  # Set to False to reduce response size
        )

        # Extract text content and metadata
        result = {
            "status": "success",
            "extracted_text": "",
            "metadata": {
                "model": getattr(ocr_response, 'model', 'mistral-ocr-latest'),
                "pages_processed": 0,
                "document_size_bytes": 0
            }
        }

        # Extract text from pages if available
        if hasattr(ocr_response, 'pages') and ocr_response.pages:
            for page in ocr_response.pages:
                result["extracted_text"] += getattr(page, 'markdown', '') + "\n"

        # Add usage information if available
        if hasattr(ocr_response, 'usage_info'):
            result["metadata"]["pages_processed"] = getattr(ocr_response.usage_info, 'pages_processed', 0)
            result["metadata"]["document_size_bytes"] = getattr(ocr_response.usage_info, 'doc_size_bytes', 0)

        # Clean up extracted text
        result["extracted_text"] = result["extracted_text"].strip()

        return result

    except Exception as e:
        error_msg = str(e)
        print(f"Error processing {image_path}: {error_msg}")
        return {
            "error": error_msg,
            "status": "failed"
        }

def main():
    # Initialize Mistral client
    api_key = os.environ.get("MISTRAL_API_KEY")
    if not api_key:
        print("Error: MISTRAL_API_KEY not found in environment variables.")
        return

    client = Mistral(api_key=api_key)

    # Get all image files from the E-Shram Card folder
    image_folder = "E-Shram Card"
    image_files = get_image_files(image_folder)

    if not image_files:
        print(f"No image files found in {image_folder} folder.")
        return

    print(f"Found {len(image_files)} files to process.")

    # Process each image
    results = {}
    successful_count = 0
    failed_count = 0

    for i, image_path in enumerate(image_files):
        filename = os.path.basename(image_path)

        # Add rate limiting - wait between requests to avoid hitting API limits
        if i > 0:
            time.sleep(2)  # Wait 2 seconds between requests

        result = process_single_image(client, image_path)

        results[filename] = {
            "file_path": image_path,
            "processed_at": datetime.now().isoformat(),
            **result
        }

        if result.get("status") == "success":
            successful_count += 1
            print(f"Successfully processed: {filename}")
        else:
            failed_count += 1
            print(f"Failed to process: {filename}")

        # Save progress every 10 files
        if (i + 1) % 10 == 0:
            temp_summary = {
                "processing_summary": {
                    "total_files": len(image_files),
                    "processed_so_far": i + 1,
                    "successful": successful_count,
                    "failed": failed_count,
                    "processed_at": datetime.now().isoformat(),
                    "model_used": "mistral-ocr-latest"
                },
                "results": results
            }

            with open("mistral_ocr_output_temp.json", 'w', encoding='utf-8') as f:
                json.dump(temp_summary, f, indent=2, ensure_ascii=False)
            print(f"Progress saved: {i + 1}/{len(image_files)} files processed")

    # Create summary
    summary = {
        "processing_summary": {
            "total_files": len(image_files),
            "successful": successful_count,
            "failed": failed_count,
            "processed_at": datetime.now().isoformat(),
            "model_used": "mistral-ocr-latest"
        },
        "results": results
    }

    # Save results to JSON file
    output_filename = "mistral_ocr_output.json"
    try:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"\nResults saved to {output_filename}")
        print(f"Summary: {successful_count} successful, {failed_count} failed out of {len(image_files)} total files")

    except Exception as e:
        print(f"Error saving results: {e}")

if __name__ == "__main__":
    main()