import base64
import os
import json
import time
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from mistralai import Mistral

# Load environment variables
load_dotenv()

def encode_image(image_path):
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except FileNotFoundError:
        print(f"Error: The file {image_path} was not found.")
        return None
    except Exception as e:
        print(f"Error encoding {image_path}: {e}")
        return None

def get_image_files(folder_path):
    image_extensions = {'.jpg', '.jpeg', '.png'}

    folder = Path(folder_path)
    if not folder.exists():
        print(f"Error: Folder {folder_path} does not exist.")
        return []

    image_files = []
    for file_path in folder.iterdir():
        if file_path.is_file():
            if file_path.suffix.lower() in image_extensions:
                image_files.append(str(file_path))

    return sorted(image_files)

def process_single_image(client, image_path):
    try:
        print(f"Processing: {image_path}")

        # Encode image to base64
        base64_image = encode_image(image_path)
        if base64_image is None:
            return {
                "error": "Failed to encode image",
                "status": "failed"
            }

        # Determine MIME type based on file extension
        file_ext = Path(image_path).suffix.lower()
        if file_ext in ['.jpg', '.jpeg']:
            mime_type = "image/jpeg"
        elif file_ext == '.png':
            mime_type = "image/png"
        else:
            mime_type = "image/jpeg"  # Default fallback

        # Call Mistral OCR API
        ocr_response = client.ocr.process(
            model="mistral-ocr-latest",
            document={
                "type": "image_url",
                "image_url": f"data:{mime_type};base64,{base64_image}"
            },
            include_image_base64=False  # Set to False to reduce response size
        )

        # Extract text content and metadata
        result = {
            "status": "success",
            "extracted_text": "",
            "metadata": {
                "model": getattr(ocr_response, 'model', 'mistral-ocr-latest'),
                "pages_processed": 0,
                "document_size_bytes": 0
            }
        }

        # Extract text from pages if available
        if hasattr(ocr_response, 'pages') and ocr_response.pages:
            for page in ocr_response.pages:
                result["extracted_text"] += getattr(page, 'markdown', '') + "\n"

        # Add usage information if available
        if hasattr(ocr_response, 'usage_info'):
            result["metadata"]["pages_processed"] = getattr(ocr_response.usage_info, 'pages_processed', 0)
            result["metadata"]["document_size_bytes"] = getattr(ocr_response.usage_info, 'doc_size_bytes', 0)

        # Clean up extracted text
        result["extracted_text"] = result["extracted_text"].strip()

        return result

    except Exception as e:
        error_msg = str(e)
        print(f"Error processing {image_path}: {error_msg}")
        return {
            "error": error_msg,
            "status": "failed"
        }

def main():
    # Initialize Mistral client
    api_key = os.environ.get("MISTRAL_API_KEY")
    if not api_key:
        print("Error: MISTRAL_API_KEY not found in environment variables.")
        return

    client = Mistral(api_key=api_key)

    # Get all image files from the E-Shram Card folder
    image_folder = "E-Shram Card"
    image_files = get_image_files(image_folder)

    if not image_files:
        print(f"No image files found in {image_folder} folder.")
        return

    print(f"Found {len(image_files)} files to process.")

    # Process each image
    results = {}
    successful_count = 0
    failed_count = 0

    for i, image_path in enumerate(image_files):
        filename = os.path.basename(image_path)

        # Add rate limiting - wait between requests to avoid hitting API limits
        if i > 0:
            time.sleep(2)  # Wait 2 seconds between requests

        result = process_single_image(client, image_path)

        results[filename] = {
            "file_path": image_path,
            "processed_at": datetime.now().isoformat(),
            **result
        }

        if result.get("status") == "success":
            successful_count += 1
            print(f"Successfully processed: {filename}")
        else:
            failed_count += 1
            print(f"Failed to process: {filename}")

        # Save progress every 10 files
        if (i + 1) % 10 == 0:
            temp_summary = {
                "processing_summary": {
                    "total_files": len(image_files),
                    "processed_so_far": i + 1,
                    "successful": successful_count,
                    "failed": failed_count,
                    "processed_at": datetime.now().isoformat(),
                    "model_used": "mistral-ocr-latest"
                },
                "results": results
            }

            with open("mistral_ocr_output_temp.json", 'w', encoding='utf-8') as f:
                json.dump(temp_summary, f, indent=2, ensure_ascii=False)
            print(f"Progress saved: {i + 1}/{len(image_files)} files processed")

    # Create summary
    summary = {
        "processing_summary": {
            "total_files": len(image_files),
            "successful": successful_count,
            "failed": failed_count,
            "processed_at": datetime.now().isoformat(),
            "model_used": "mistral-ocr-latest"
        },
        "results": results
    }

    # Save results to JSON file
    output_filename = "mistral_ocr_output.json"
    try:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"\nResults saved to {output_filename}")
        print(f"Summary: {successful_count} successful, {failed_count} failed out of {len(image_files)} total files")

    except Exception as e:
        print(f"Error saving results: {e}")

def extract_information_from_ocr(ocr_text, filename):
    """
    Extract specific information from OCR text for e-SHRAM cards
    """
    import re

    # Initialize result dictionary
    result = {
        'filename': filename,
        'document_name': '',
        'issuing_authority': '',
        'person_name': '',
        'date_of_birth': '',
        'gender': '',
        'uan_number': '',
        'current_address': ''
    }

    # Clean the OCR text
    text = ocr_text.replace('\n', ' ').replace('|', ' ')

    # Extract document name
    if 'e-SHRAM Card' in ocr_text or 'ई-श्रम कार्ड' in ocr_text:
        result['document_name'] = 'e-SHRAM Card'

    # Extract issuing authority
    if 'MINISTRY OF LABOUR & EMPLOYMENT' in ocr_text:
        result['issuing_authority'] = 'Ministry of Labour & Employment, Govt. of India'

    # Extract person name - look for patterns after "नाम / Name" or "Name"
    name_patterns = [
        r'नाम\s*/\s*Name\s*:?\s*([^|]+?)(?:\s*पिता|पति|\s*Father|\s*Husband|\s*जन्म|\s*DOB)',
        r'Name\s*:?\s*([^|]+?)(?:\s*Father|\s*Husband|\s*DOB|\s*पिता|पति)',
        r'नाम\s*:?\s*([^|]+?)(?:\s*पिता|पति|\s*Father|\s*Husband)'
    ]

    for pattern in name_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            name = match.group(1).strip()
            # Clean up the name
            name = re.sub(r'[^\w\s/]', '', name).strip()
            if name and len(name) > 2:
                result['person_name'] = name
                break

    # Extract date of birth - look for DD/MM/YYYY pattern
    dob_patterns = [
        r'जन्म\s*तिथि\s*/\s*DOB\s*:?\s*(\d{1,2}/\d{1,2}/\d{4})',
        r'DOB\s*:?\s*(\d{1,2}/\d{1,2}/\d{4})',
        r'(\d{1,2}/\d{1,2}/\d{4})'
    ]

    for pattern in dob_patterns:
        match = re.search(pattern, text)
        if match:
            result['date_of_birth'] = match.group(1)
            break

    # Extract gender
    if 'Male' in ocr_text or 'पुरुष' in ocr_text:
        result['gender'] = 'Male'
    elif 'Female' in ocr_text or 'महिला' in ocr_text:
        result['gender'] = 'Female'

    # Extract UAN number - look for pattern like "7XXX XXXX XXXX"
    uan_patterns = [
        r'Universal\s*Account\s*Number\s*\(UAN\)\s*(\d{4}\s*\d{4}\s*\d{4})',
        r'UAN\s*(\d{4}\s*\d{4}\s*\d{4})',
        r'(\d{4}\s*\d{4}\s*\d{4})'
    ]

    for pattern in uan_patterns:
        match = re.search(pattern, text)
        if match:
            uan = match.group(1).replace(' ', '')
            if len(uan) == 12 and uan.startswith('7'):
                result['uan_number'] = match.group(1)
                break

    # Extract current address - look for patterns after "Current Address"
    address_patterns = [
        r'Current\s*Address\s*:?\s*([^|]+?)(?:\s*Contact|\s*To\s*reprint|\s*eshram)',
        r'Current\s*Address\s*([^|]+?)(?:\s*Contact|\s*To\s*reprint|\s*eshram)'
    ]

    for pattern in address_patterns:
        match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if match:
            address = match.group(1).strip()
            # Clean up the address
            address = re.sub(r'\s+', ' ', address).strip()
            if address and len(address) > 10:
                result['current_address'] = address
                break

    return result

def process_ocr_results_to_csv(json_file_path, csv_file_path):
    """
    Process OCR results from JSON file and create CSV with extracted information
    """
    import csv

    try:
        # Load OCR results
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Extract information from each result
        extracted_data = []

        for filename, result_data in data.get('results', {}).items():
            if result_data.get('status') == 'success':
                ocr_text = result_data.get('extracted_text', '')
                extracted_info = extract_information_from_ocr(ocr_text, filename)
                extracted_data.append(extracted_info)
                print(f"Processed: {filename}")
            else:
                # Handle failed OCR
                extracted_info = {
                    'filename': filename,
                    'document_name': 'OCR_FAILED',
                    'issuing_authority': 'OCR_FAILED',
                    'person_name': 'OCR_FAILED',
                    'date_of_birth': 'OCR_FAILED',
                    'gender': 'OCR_FAILED',
                    'uan_number': 'OCR_FAILED',
                    'current_address': 'OCR_FAILED'
                }
                extracted_data.append(extracted_info)
                print(f"Failed OCR: {filename}")

        # Write to CSV
        fieldnames = ['filename', 'document_name', 'issuing_authority', 'person_name',
                     'date_of_birth', 'gender', 'uan_number', 'current_address']

        with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(extracted_data)

        print(f"\nCSV file created successfully: {csv_file_path}")
        print(f"Total records processed: {len(extracted_data)}")

        return extracted_data

    except Exception as e:
        print(f"Error processing OCR results: {e}")
        return None

if __name__ == "__main__":
    # Check if we should run OCR or just process existing results
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "extract":
        # Process existing OCR results to CSV
        json_file = "mistral_ocr_output.json"
        csv_file = "extracted_information.csv"

        if os.path.exists(json_file):
            print("Processing existing OCR results...")
            process_ocr_results_to_csv(json_file, csv_file)
        else:
            print(f"OCR results file {json_file} not found. Run OCR first.")
    else:
        # Run OCR processing
        main()